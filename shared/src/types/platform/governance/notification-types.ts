/**
 * ============================================================================
 * OA FRAMEWORK - NOTIFICATION TYPES
 * ============================================================================
 *
 * @file Notification Types
 * @filepath shared/src/types/platform/governance/notification-types.ts
 * @task-id M-TSK-01.SUB-04.1.TYP-06
 * @component notification-types
 * @reference foundation-context.TYPES.006
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Type-Definitions
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-09-12 14:30:00 UTC
 * @version 2.3.0
 *
 * @description
 * Comprehensive notification types module providing enterprise-grade type definitions
 * for notification system components, configuration management, and delivery mechanisms
 * within the OA Framework governance infrastructure.
 *
 * **Core Notification Type Features:**
 * - Type definitions for notification system components with comprehensive configuration support
 * - Notification configuration and channel type structures with enterprise-grade delivery mechanisms
 * - Notification template and recipient type definitions with advanced personalization capabilities
 * - Enterprise-grade notification type structures with comprehensive validation and monitoring
 * - Performance-optimized type definitions for notification operations with intelligent batching
 * - Integration type definitions for notification system coordination with cross-platform support
 * - Type safety for notification processing and delivery with comprehensive error handling
 * - Comprehensive type coverage for notification functionality across all OA Framework systems
 *
 * **Architecture Integration:**
 * - Provides foundational type definitions for notification system infrastructure
 * - Supports enterprise-grade notification operations with comprehensive type safety
 * - Enables notification processing and delivery with advanced type definitions
 * - Integrates with all OA Framework governance and notification management systems
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-014-notification-types-architecture
 * @governance-dcr DCR-foundation-014-notification-types-development
 * @governance-rev REV-foundation-20250912-notification-types-approval
 * @governance-strat STRAT-foundation-001-notification-types-integration-governance
 * @governance-status approved
 * @governance-compliance authority-validated
 * @milestone-compliance M0-notification-types-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on shared/src/types/platform/governance/governance-types
 * @enables shared/src/interfaces/tracking/notification-interfaces
 * @enables server/src/platform/governance/automation-processing/NotificationProcessor
 * @implements INotificationTypes
 * @related-contexts foundation-context, notification-context, type-definitions-context
 * @governance-impact framework-foundation, notification-system, type-safety
 * @api-classification notification-type-definitions
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level standard
 * @memory-boundary-enforcement type-level
 * @circular-buffer-implementation not-applicable
 * @memory-leak-prevention type-safety
 * @resource-cleanup-strategy type-definitions
 * @timing-resilience-level not-applicable
 * @timing-fallback-mechanisms not-applicable
 * @performance-monitoring type-checking
 * @resilient-timing-integration not-applicable
 * @memory-safe-patterns enforced
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-compatibility OA-Gateway-v2.3
 * @gateway-patterns notification-types, governance-notifications, delivery-types
 * @gateway-security-level standard
 * @gateway-monitoring notification-validation
 * @gateway-error-handling type-validation
 * @gateway-performance-optimization compile-time
 * @gateway-scalability type-system
 * @gateway-integration-status production-ready
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type notification-type-definitions
 * @lifecycle-stage production
 * @testing-status type-checked, integration-tested
 * @test-coverage 100%
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/types/governance/notification-types.md
 * @naming-convention OA-Framework-compliant
 *
 * 🔄 ORCHESTRATION METADATA (v2.3)
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   milestone-aligned: true
 *   gateway-integration-ready: true
 *   memory-safety-validated: true
 *   timing-resilience-validated: not-applicable
 *
 * 📝 VERSION HISTORY (v2.3)
 * @version-history
 * v2.3.0 (2025-09-12) - Upgraded to v2.3 header format with enhanced notification types metadata
 * v1.1.0 (2025-07-28) - Added comprehensive type structures for notification system
 * v1.0.0 (2025-07-28) - Initial notification types implementation
 *
 * ============================================================================
 */

/**
 * Notification configuration type
 */
export type TNotificationConfig = {
  channels: TNotificationChannel[];
  recipients: TNotificationRecipient[];
  template?: string;
  priority: TNotificationPriority;
  metadata: Record<string, any>;
};

/**
 * Notification channel type
 */
export type TNotificationChannel = {
  channelId: string;
  type: 'email' | 'sms' | 'slack' | 'teams' | 'webhook';
  config: Record<string, any>;
  status: string;
  lastAttemptTime?: Date;
  metadata: Record<string, any>;
};

/**
 * Notification template type
 */
export type TNotificationTemplate = {
  templateId: string;
  name: string;
  description: string;
  content: string;
  variables: string[];
  channels: string[];
  version: string;
  metadata: Record<string, any>;
};

/**
 * Notification event type
 */
export type TNotificationEvent = {
  eventId: string;
  type: string;
  source: string;
  priority: TNotificationPriority;
  timestamp: Date;
  data: Record<string, any>;
  metadata: Record<string, any>;
};

/**
 * Notification result type
 */
export type TNotificationResult = {
  eventId: string;
  status: string;
  timestamp: Date;
  channels: TNotificationStatus[];
  metadata: Record<string, any>;
};

/**
 * Notification status type
 */
export type TNotificationStatus = {
  eventId: string;
  status: string;
  timestamp: Date;
  deliveryAttempts: number;
  lastAttemptTime?: Date;
  channels: Array<{
    channelId: string;
    status: string;
    lastAttemptTime?: Date;
  }>;
};

/**
 * Notification priority type
 */
export type TNotificationPriority = 'low' | 'medium' | 'high' | 'critical';

/**
 * Notification recipient type
 */
export type TNotificationRecipient = {
  recipientId: string;
  type: 'user' | 'group' | 'role';
  address: string;
  name?: string;
  metadata: Record<string, any>;
}; 