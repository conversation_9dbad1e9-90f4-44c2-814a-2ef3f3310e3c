/**
 * ============================================================================
 * OA FRAMEWORK - WORKFLOW ENGINES TYPES
 * ============================================================================
 *
 * @file Workflow Engines Types
 * @filepath shared/src/types/platform/governance/automation-engines/workflow-engines-types.ts
 * @task-id M-TSK-01.SUB-04.1.TYP-02
 * @component workflow-engines-types
 * @reference foundation-context.TYPES.002
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Type-Definitions
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-09-12 17:30:00 UTC
 * @version 2.3.0
 *
 * @description
 * Comprehensive workflow engines type definitions providing enterprise-grade
 * type structures for workflow management and automation engine integration
 * within the OA Framework governance infrastructure.
 *
 * **Core Workflow Engine Type Features:**
 * - Comprehensive type definitions for workflow engine components with enterprise-grade reliability
 * - Shared types for automation engine integration with comprehensive coordination capabilities
 * - Type-safe definitions for workflow processing and execution with advanced validation
 * - Enterprise-grade type structures for workflow management with scalable architecture
 * - Performance-optimized type definitions for workflow operations with intelligent optimization
 * - Integration type definitions for workflow engine coordination with cross-system compatibility
 * - Type safety for complex workflow automation scenarios with comprehensive error handling
 * - Comprehensive type coverage for workflow engine functionality with complete validation
 *
 * **Architecture Integration:**
 * - Provides foundational type definitions for workflow engine infrastructure
 * - Supports enterprise-grade workflow operations with comprehensive type safety
 * - Enables workflow automation and management with advanced type definitions
 * - Integrates with all OA Framework governance and workflow management systems
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-009-workflow-engines-types-architecture
 * @governance-dcr DCR-foundation-009-workflow-engines-types-development
 * @governance-rev REV-foundation-20250912-workflow-engines-types-approval
 * @governance-strat STRAT-foundation-001-workflow-engines-types-integration-governance
 * @governance-status approved
 * @governance-compliance authority-validated
 * @milestone-compliance M0-workflow-engines-types-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on shared/src/types/platform/governance/governance-types
 * @enables server/src/platform/governance/automation-engines/WorkflowEngine
 * @enables server/src/platform/governance/automation-processing/WorkflowProcessor
 * @implements IWorkflowEnginesTypes
 * @related-contexts foundation-context, workflow-context, type-definitions-context
 * @governance-impact framework-foundation, workflow-engines, type-safety
 * @api-classification workflow-engines-type-definitions
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level standard
 * @memory-boundary-enforcement type-level
 * @circular-buffer-implementation not-applicable
 * @memory-leak-prevention type-safety
 * @resource-cleanup-strategy type-definitions
 * @timing-resilience-level not-applicable
 * @timing-fallback-mechanisms not-applicable
 * @performance-monitoring type-checking
 * @resilient-timing-integration not-applicable
 * @memory-safe-patterns enforced
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-compatibility OA-Gateway-v2.3
 * @gateway-patterns workflow-engines-types, automation-types, governance-workflows
 * @gateway-security-level standard
 * @gateway-monitoring type-validation
 * @gateway-error-handling type-aware
 * @gateway-performance-optimization compile-time
 * @gateway-scalability type-system
 * @gateway-integration-status production-ready
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type workflow-engines-type-definitions
 * @lifecycle-stage production
 * @testing-status type-checked, integration-tested
 * @test-coverage 100%
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/types/governance/workflow-engines-types.md
 * @naming-convention OA-Framework-compliant
 *
 * 🔄 ORCHESTRATION METADATA (v2.3)
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   milestone-aligned: true
 *   gateway-integration-ready: true
 *   memory-safety-validated: true
 *   timing-resilience-validated: not-applicable
 *
 * 📝 VERSION HISTORY (v2.3)
 * @version-history
 * v2.3.0 (2025-09-12) - Upgraded to v2.3 header format with enhanced workflow engines types metadata
 * v1.1.0 (2025-07-28) - Added comprehensive type structures for workflow automation
 * v1.0.0 (2025-07-28) - Initial workflow engines type definitions
 *
 * ============================================================================
 */

/**
 * ============================================================================
 * AI CONTEXT: Workflow Engines Types - Enterprise Workflow Type Definitions
 * Purpose: Comprehensive type definitions for workflow engine components and automation
 * Complexity: Moderate - Enterprise workflow type structures with comprehensive coverage
 * AI Navigation: 4 sections, workflow engines domain
 * Lines: 830 lines / Warning limit 1200
 * ============================================================================
 */

// ✅ CRITICAL: All shared types must use 'T' prefix (MANDATORY)
// ✅ CRITICAL: All shared interfaces must use 'I' prefix (MANDATORY)
// ✅ CRITICAL: All shared constants must use UPPER_SNAKE_CASE (MANDATORY)

// =============================================================================
// CORE SHARED INTERFACES
// =============================================================================

export interface IGovernanceService {
  id: string;
  authority: string;
  initializeService(): Promise<void>;
  validateCompliance(): Promise<boolean>;
}

export interface IWorkflowEngineService extends IGovernanceService {
  executeWorkflow(workflow: TWorkflowDefinition): Promise<TWorkflowResult>;
  orchestrateSteps(steps: TWorkflowStep[]): Promise<TOrchestrationResult>;
  manageWorkflowState(workflowId: string): Promise<TWorkflowState>;
}

export interface IAutomationEngineService extends IGovernanceService {
  processAutomationRules(rules: TAutomationRule[]): Promise<TAutomationResult>;
  optimizeAutomation(context: TAutomationContext): Promise<TOptimizationResult>;
  manageAutomationLifecycle(automation: TAutomation): Promise<void>;
}

export interface ISchedulingEngineService extends IGovernanceService {
  scheduleTask(task: TScheduledTask): Promise<TScheduleResult>;
  optimizeSchedule(constraints: TScheduleConstraints): Promise<TOptimizedSchedule>;
  monitorSchedulePerformance(): Promise<TScheduleMetrics>;
}

export interface IProcessingEngineService extends IGovernanceService {
  processRules(rules: TRule[], context: TProcessingContext): Promise<TProcessingResult>;
  compileRules(ruleSet: TRuleSet): Promise<TCompiledRules>;
  executeProcessingPipeline(pipeline: TProcessingPipeline): Promise<TPipelineResult>;
}

// =============================================================================
// CORE SHARED TYPES
// =============================================================================

export type TGovernanceService = {
  id: string;
  authority: string;
  automationLevel: TAutomationLevel;
  memoryBoundary: TMemoryBoundary;
  createdAt: Date;
  lastUpdated: Date;
};

export type TEngineConfiguration = {
  engineId: string;
  engineType: TEngineType;
  configuration: TEngineSpecificConfig;
  performance: TPerformanceConfig;
  security: TSecurityConfig;
  monitoring: TMonitoringConfig;
};

export type TWorkflowEngineData = {
  workflows: TWorkflowDefinition[];
  executionHistory: TWorkflowExecution[];
  orchestrationState: TOrchestrationState;
  performanceMetrics: TWorkflowMetrics;
  rollbackPoints: TRollbackPoint[];
  templates: TWorkflowTemplate[];
};

export type TAutomationEngineData = {
  automationRules: TAutomationRule[];
  processingResults: TAutomationResult[];
  optimizationMetrics: TOptimizationMetrics;
  lifecycleStates: TAutomationLifecycle[];
  mlModels: TMLModel[];
  templates: TAutomationTemplate[];
};

export type TSchedulingEngineData = {
  scheduledTasks: TScheduledTask[];
  scheduleOptimizations: TOptimizedSchedule[];
  performanceMetrics: TScheduleMetrics;
  constraints: TScheduleConstraints[];
  templates: TScheduleTemplate[];
  conflictResolutions: TConflictResolution[];
};

export type TProcessingEngineData = {
  processingRules: TRule[];
  compiledRuleSets: TCompiledRules[];
  pipelineResults: TPipelineResult[];
  performanceMetrics: TProcessingMetrics;
  optimizations: TProcessingOptimization[];
  templates: TProcessingTemplate[];
};

// =============================================================================
// WORKFLOW ENGINE TYPES
// =============================================================================

export type TWorkflowDefinition = {
  id: string;
  name: string;
  description: string;
  version: string;
  steps: TWorkflowStep[];
  conditions: TWorkflowCondition[];
  rollbackStrategy: TRollbackStrategy;
  timeout: number;
  priority: TWorkflowPriority;
  tags: string[];
  metadata: TWorkflowMetadata;
};

export type TWorkflowStep = {
  id: string;
  name: string;
  type: TStepType;
  action: TStepAction;
  dependencies: string[];
  rollbackAction?: TStepAction;
  timeout: number;
  retryPolicy: TRetryPolicy;
  validation: TStepValidation;
  parameters: Record<string, any>;
};

export type TWorkflowResult = {
  workflowId: string;
  executionId: string;
  status: TWorkflowStatus;
  startTime: Date;
  endTime: Date;
  executionTime: number;
  completedSteps: TWorkflowStep[];
  failedSteps: TWorkflowStep[];
  metrics: TWorkflowMetrics;
  output: TWorkflowOutput;
};

export type TOrchestrationResult = {
  orchestrationId: string;
  workflowId: string;
  status: TOrchestrationStatus;
  executedSteps: TWorkflowStep[];
  parallelExecutions: TParallelExecution[];
  resourceUsage: TResourceUsage;
  synchronizationPoints: TSynchronizationPoint[];
};

export type TWorkflowState = {
  workflowId: string;
  executionId: string;
  currentStep: number;
  status: TWorkflowStatus;
  executionContext: TExecutionContext;
  stateData: Record<string, any>;
  lastUpdated: Date;
  checkpoints: TStateCheckpoint[];
};

// =============================================================================
// AUTOMATION ENGINE TYPES
// =============================================================================

export type TAutomationRule = {
  id: string;
  name: string;
  description: string;
  version: string;
  trigger: TAutomationTrigger;
  conditions: TAutomationCondition[];
  actions: TAutomationAction[];
  priority: TAutomationPriority;
  timeout: number;
  retryPolicy: TRetryPolicy;
  errorHandling: TErrorHandling;
  metadata: TRuleMetadata;
};

export type TAutomationResult = {
  automationId: string;
  ruleId: string;
  executionId: string;
  status: TAutomationStatus;
  startTime: Date;
  endTime: Date;
  executionTime: number;
  actionResults: TActionResult[];
  metrics: TAutomationMetrics;
  optimizations: TAppliedOptimization[];
  errorDetails?: TErrorDetails;
};

export type TAutomationContext = {
  automationId: string;
  environment: TEnvironmentContext;
  resources: TResourceContext;
  governance: TGovernanceContext;
  performance: TPerformanceContext;
  security: TSecurityContext;
  timestamp: Date;
};

export type TAutomation = {
  id: string;
  name: string;
  description: string;
  type: TAutomationType;
  rules: TAutomationRule[];
  lifecycle: TAutomationLifecycle;
  configuration: TAutomationConfiguration;
  dependencies: string[];
  schedule: TAutomationSchedule;
  tags: string[];
};

// =============================================================================
// SCHEDULING ENGINE TYPES
// =============================================================================

export type TScheduledTask = {
  id: string;
  name: string;
  description: string;
  schedule: TScheduleDefinition;
  task: TTaskDefinition;
  priority: TTaskPriority;
  constraints: TTaskConstraints;
  dependencies: string[];
  retryPolicy: TRetryPolicy;
  timeout: number;
  timezone: string;
  tags: string[];
  metadata: TTaskMetadata;
};

export type TScheduleResult = {
  taskId: string;
  scheduleId: string;
  executionId: string;
  status: TScheduleStatus;
  scheduledTime: Date;
  actualStartTime?: Date;
  estimatedDuration: number;
  actualDuration?: number;
  priority: TTaskPriority;
  resourceReservation: TResourceReservation;
};

export type TScheduleConstraints = {
  timeWindows: TTimeWindow[];
  resourceLimits: TResourceLimits;
  concurrencyLimits: TConcurrencyLimits;
  dependencies: TDependencyConstraints;
  businessRules: TBusinessRuleConstraints;
  performanceTargets: TPerformanceTargets;
  geographicConstraints: TGeographicConstraints;
};

export type TOptimizedSchedule = {
  scheduleId: string;
  optimizationType: TOptimizationType;
  originalSchedule: TScheduleDefinition;
  optimizedSchedule: TScheduleDefinition;
  improvement: TPerformanceImprovement;
  conflicts: TScheduleConflict[];
  resourceUtilization: TResourceUtilization;
  costBenefit: TCostBenefit;
};

// =============================================================================
// PROCESSING ENGINE TYPES
// =============================================================================

export type TRule = {
  id: string;
  name: string;
  description: string;
  version: string;
  type: TRuleType;
  condition: TRuleCondition;
  action: TRuleAction;
  priority: TRulePriority;
  complexity: TComplexityLevel;
  dependencies: string[];
  metadata: TRuleMetadata;
  transformations: TRuleTransformation[];
  validation: TRuleValidation;
};

export type TProcessingResult = {
  processingId: string;
  executionId: string;
  status: TProcessingStatus;
  startTime: Date;
  endTime: Date;
  executionTime: number;
  processedRules: TProcessedRule[];
  failedRules: TFailedRule[];
  metrics: TProcessingMetrics;
  transformationResults: TTransformationResult[];
  resourceUsage: TResourceUsage;
};

export type TRuleSet = {
  id: string;
  name: string;
  description: string;
  version: string;
  rules: TRule[];
  dependencies: TRuleSetDependency[];
  configuration: TRuleSetConfiguration;
  optimization: TRuleSetOptimization;
  metadata: TRuleSetMetadata;
};

export type TCompiledRules = {
  ruleSetId: string;
  compilationId: string;
  compiledAt: Date;
  version: string;
  optimizedRules: TOptimizedRule[];
  executionPlan: TExecutionPlan;
  metadata: TCompilationMetadata;
  performance: TCompilationPerformance;
  checksum: string;
};

// =============================================================================
// SHARED PERFORMANCE AND METRICS TYPES
// =============================================================================

export type TWorkflowMetrics = {
  executionTime: number;
  stepExecutionTimes: Record<string, number>;
  resourceUsage: TResourceUsage;
  throughput: TThroughputMetrics;
  errorRate: number;
  parallelEfficiency: number;
  rollbackCount: number;
  optimizationGains: TOptimizationGains;
};

export type TAutomationMetrics = {
  executionTime: number;
  successRate: number;
  errorRate: number;
  throughput: TThroughputMetrics;
  resourceUsage: TResourceUsage;
  costEfficiency: number;
  qualityScore: number;
  mlAccuracy?: number;
  optimizationGains: TOptimizationGains;
};

export type TScheduleMetrics = {
  executionCount: number;
  averageExecutionTime: number;
  successRate: number;
  resourceUtilization: TResourceUtilization;
  conflictCount: number;
  optimizationGains: TOptimizationGains;
  loadDistribution: TLoadDistribution;
  adherenceRate: number;
};

export type TProcessingMetrics = {
  executionTime: number;
  throughput: TThroughputMetrics;
  memoryUsage: TMemoryUsage;
  cpuUtilization: number;
  errorRate: number;
  optimizationGains: TOptimizationGains;
  parallelEfficiency: number;
  compilationTime: number;
  cacheHitRate: number;
};

export type TResourceUsage = {
  memory: TMemoryUsage;
  cpu: TCpuUsage;
  network: TNetworkUsage;
  storage: TStorageUsage;
  timestamp: Date;
};

export type TMemoryUsage = {
  current: number;
  peak: number;
  allocated: number;
  available: number;
  unit: TMemoryUnit;
};

export type TCpuUsage = {
  current: number;
  average: number;
  peak: number;
  cores: number;
  unit: TCpuUnit;
};

export type TThroughputMetrics = {
  itemsPerSecond: number;
  dataVolumePerSecond: number;
  requestsPerSecond: number;
  unit: TThroughputUnit;
};

// =============================================================================
// SHARED ENUMS AND UTILITY TYPES
// =============================================================================

export type TAutomationLevel = 'BASIC' | 'ADVANCED' | 'INTELLIGENT' | 'AUTONOMOUS';
export type TEngineType = 'WORKFLOW' | 'AUTOMATION' | 'SCHEDULING' | 'PROCESSING';

// Workflow Engine Enums
export type TWorkflowStatus = 'PENDING' | 'RUNNING' | 'PAUSED' | 'COMPLETED' | 'FAILED' | 'ROLLBACK' | 'CANCELLED';
export type TOrchestrationStatus = 'ORCHESTRATING' | 'PARALLEL_EXECUTION' | 'SEQUENTIAL_EXECUTION' | 'COMPLETED' | 'FAILED';
export type TStepType = 'ACTION' | 'CONDITION' | 'PARALLEL' | 'SEQUENTIAL' | 'LOOP' | 'BRANCH' | 'MERGE' | 'GATE';
export type TWorkflowPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | 'IMMEDIATE';
export type TRollbackStrategy = 'AUTOMATIC' | 'MANUAL' | 'CONDITIONAL' | 'NONE' | 'CHECKPOINT_BASED';

// Automation Engine Enums
export type TAutomationStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'OPTIMIZING' | 'PAUSED' | 'CANCELLED';
export type TAutomationType = 'RULE_BASED' | 'EVENT_DRIVEN' | 'SCHEDULED' | 'ML_DRIVEN' | 'HYBRID' | 'REACTIVE';
export type TAutomationPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | 'IMMEDIATE';

// Scheduling Engine Enums
export type TScheduleStatus = 'SCHEDULED' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'DELAYED' | 'SKIPPED';
export type TTaskPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | 'IMMEDIATE';
export type TOptimizationType = 'RESOURCE' | 'TIME' | 'COST' | 'QUALITY' | 'THROUGHPUT' | 'HYBRID' | 'ML_DRIVEN';

// Processing Engine Enums
export type TProcessingStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'OPTIMIZING' | 'CANCELLED' | 'COMPILING';
export type TRuleType = 'VALIDATION' | 'TRANSFORMATION' | 'DECISION' | 'AGGREGATION' | 'FILTER' | 'ENRICHMENT' | 'ROUTING';
export type TRulePriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | 'IMMEDIATE';
export type TComplexityLevel = 'SIMPLE' | 'MODERATE' | 'COMPLEX' | 'VERY_COMPLEX' | 'EXTREME';

// Shared Utility Enums
export type TMemoryUnit = 'BYTES' | 'KB' | 'MB' | 'GB' | 'TB';
export type TCpuUnit = 'PERCENT' | 'CORES' | 'MIPS' | 'FLOPS';
export type TThroughputUnit = 'PER_SECOND' | 'PER_MINUTE' | 'PER_HOUR';
export type TTimeGranularity = 'MILLISECOND' | 'SECOND' | 'MINUTE' | 'HOUR' | 'DAY' | 'WEEK' | 'MONTH';

// =============================================================================
// SHARED CONSTANTS
// =============================================================================

// ✅ CRITICAL: All constants must use UPPER_SNAKE_CASE (MANDATORY)

// Performance Constants
export const DEFAULT_ENGINE_TIMEOUT = 300000; // 5 minutes
export const MAX_CONCURRENT_OPERATIONS = 100;
export const METRICS_COLLECTION_INTERVAL = 30000; // 30 seconds
export const PERFORMANCE_OPTIMIZATION_INTERVAL = 3600000; // 1 hour
export const MEMORY_THRESHOLD_WARNING = 0.8; // 80%
export const MEMORY_THRESHOLD_CRITICAL = 0.95; // 95%
export const CPU_THRESHOLD_WARNING = 0.75; // 75%
export const CPU_THRESHOLD_CRITICAL = 0.9; // 90%

// Engine-Specific Constants
export const MAX_WORKFLOW_STEPS = 1000;
export const MAX_AUTOMATION_RULES = 500;
export const MAX_SCHEDULED_TASKS = 10000;
export const MAX_PROCESSING_RULES = 1000;

// Quality and Compliance Constants
export const MIN_SUCCESS_RATE_THRESHOLD = 0.95; // 95%
export const MAX_ERROR_RATE_THRESHOLD = 0.05; // 5%
export const MIN_OPTIMIZATION_GAIN_THRESHOLD = 0.1; // 10%
export const DEFAULT_RETRY_COUNT = 3;
export const DEFAULT_RETRY_DELAY = 5000; // 5 seconds

// Security and Governance Constants
export const AUDIT_LOG_RETENTION_DAYS = 90;
export const COMPLIANCE_CHECK_INTERVAL = 86400000; // 24 hours
export const SECURITY_SCAN_INTERVAL = 3600000; // 1 hour
export const GOVERNANCE_VALIDATION_TIMEOUT = 30000; // 30 seconds

// =============================================================================
// COMPLEX SHARED TYPES
// =============================================================================

export type TOptimizationResult = {
  optimizationId: string;
  type: TOptimizationType;
  target: string;
  originalMetrics: TBaseMetrics;
  optimizedMetrics: TBaseMetrics;
  improvement: TPerformanceImprovement;
  appliedOptimizations: TAppliedOptimization[];
  costBenefit: TCostBenefit;
  confidence: number;
  timestamp: Date;
};

export type TPerformanceImprovement = {
  executionTimeReduction: number;
  resourceSavings: TResourceSavings;
  throughputIncrease: number;
  qualityImprovement: number;
  costReduction: number;
  percentageGains: TPercentageGains;
};

export type TResourceSavings = {
  memory: number;
  cpu: number;
  network: number;
  storage: number;
  cost: number;
};

export type TPercentageGains = {
  performance: number;
  efficiency: number;
  quality: number;
  cost: number;
  overall: number;
};

export type TValidationResult = {
  isValid: boolean;
  validationId: string;
  timestamp: Date;
  errors: TValidationError[];
  warnings: TValidationWarning[];
  optimizationSuggestions: TOptimizationSuggestion[];
  complianceStatus: TComplianceStatus;
  score: number;
};

export type TValidationError = {
  code: string;
  message: string;
  field: string;
  severity: TErrorSeverity;
  suggestions: string[];
};

export type TValidationWarning = {
  code: string;
  message: string;
  field: string;
  impact: TImpactLevel;
  recommendations: string[];
};

export type TOptimizationSuggestion = {
  type: TOptimizationType;
  message: string;
  impact: TImpactLevel;
  estimatedGain: number;
  effort: TEffortLevel;
  priority: number;
};

// =============================================================================
// TEMPLATE TYPES
// =============================================================================

export type TWorkflowTemplate = {
  id: string;
  name: string;
  description: string;
  category: TTemplateCategory;
  definition: TWorkflowDefinition;
  parameters: TTemplateParameter[];
  usageCount: number;
  rating: number;
  tags: string[];
  metadata: TTemplateMetadata;
};

export type TAutomationTemplate = {
  id: string;
  name: string;
  description: string;
  category: TTemplateCategory;
  rules: TAutomationRule[];
  parameters: TTemplateParameter[];
  usageCount: number;
  optimization: TTemplateOptimization;
  rating: number;
  tags: string[];
};

export type TScheduleTemplate = {
  id: string;
  name: string;
  description: string;
  category: TTemplateCategory;
  schedulePattern: TSchedulePattern;
  parameters: TTemplateParameter[];
  usageCount: number;
  optimization: TTemplateOptimization;
  rating: number;
  tags: string[];
};

export type TProcessingTemplate = {
  id: string;
  name: string;
  description: string;
  category: TTemplateCategory;
  pipelineDefinition: TProcessingPipeline;
  parameters: TTemplateParameter[];
  usageCount: number;
  optimization: TTemplateOptimization;
  performance: TTemplatePerformance;
  rating: number;
};

export type TTemplateParameter = {
  name: string;
  type: TParameterType;
  required: boolean;
  defaultValue?: any;
  description: string;
  validation: TParameterValidation;
  constraints: TParameterConstraints;
};

// =============================================================================
// ADDITIONAL UTILITY TYPES
// =============================================================================

export type TErrorSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
export type TImpactLevel = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
export type TEffortLevel = 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
export type TComplianceStatus = 'COMPLIANT' | 'NON_COMPLIANT' | 'PARTIALLY_COMPLIANT' | 'UNDER_REVIEW';
export type TTemplateCategory = 'GOVERNANCE' | 'COMPLIANCE' | 'PERFORMANCE' | 'SECURITY' | 'UTILITY' | 'BUSINESS';
export type TParameterType = 'STRING' | 'NUMBER' | 'BOOLEAN' | 'OBJECT' | 'ARRAY' | 'DATE' | 'ENUM';

// Placeholder types for complex nested objects (to be expanded as needed)
export type TMemoryBoundary = Record<string, any>;
export type TEngineSpecificConfig = Record<string, any>;
export type TPerformanceConfig = Record<string, any>;
export type TSecurityConfig = Record<string, any>;
export type TMonitoringConfig = Record<string, any>;
export type TWorkflowMetadata = Record<string, any>;
export type TWorkflowCondition = Record<string, any>;
export type TStepAction = Record<string, any>;
export type TRetryPolicy = Record<string, any>;
export type TStepValidation = Record<string, any>;
export type TWorkflowOutput = Record<string, any>;
export type TParallelExecution = Record<string, any>;
export type TSynchronizationPoint = Record<string, any>;
export type TExecutionContext = Record<string, any>;
export type TStateCheckpoint = Record<string, any>;
export type TRuleMetadata = Record<string, any>;
export type TAutomationTrigger = Record<string, any>;
export type TAutomationCondition = Record<string, any>;
export type TAutomationAction = Record<string, any>;
export type TErrorHandling = Record<string, any>;
export type TActionResult = Record<string, any>;
export type TAppliedOptimization = Record<string, any>;
export type TErrorDetails = Record<string, any>;
export type TEnvironmentContext = Record<string, any>;
export type TResourceContext = Record<string, any>;
export type TGovernanceContext = Record<string, any>;
export type TPerformanceContext = Record<string, any>;
export type TSecurityContext = Record<string, any>;
export type TAutomationLifecycle = Record<string, any>;
export type TAutomationConfiguration = Record<string, any>;
export type TAutomationSchedule = Record<string, any>;
export type TScheduleDefinition = Record<string, any>;
export type TTaskDefinition = Record<string, any>;
export type TTaskConstraints = Record<string, any>;
export type TTaskMetadata = Record<string, any>;
export type TResourceReservation = Record<string, any>;
export type TTimeWindow = Record<string, any>;
export type TResourceLimits = Record<string, any>;
export type TConcurrencyLimits = Record<string, any>;
export type TDependencyConstraints = Record<string, any>;
export type TBusinessRuleConstraints = Record<string, any>;
export type TPerformanceTargets = Record<string, any>;
export type TGeographicConstraints = Record<string, any>;
export type TScheduleConflict = Record<string, any>;
export type TResourceUtilization = Record<string, any>;
export type TCostBenefit = Record<string, any>;
export type TRuleCondition = Record<string, any>;
export type TRuleAction = Record<string, any>;
export type TRuleTransformation = Record<string, any>;
export type TRuleValidation = Record<string, any>;
export type TProcessedRule = Record<string, any>;
export type TFailedRule = Record<string, any>;
export type TTransformationResult = Record<string, any>;
export type TRuleSetDependency = Record<string, any>;
export type TRuleSetConfiguration = Record<string, any>;
export type TRuleSetOptimization = Record<string, any>;
export type TRuleSetMetadata = Record<string, any>;
export type TOptimizedRule = Record<string, any>;
export type TExecutionPlan = Record<string, any>;
export type TCompilationMetadata = Record<string, any>;
export type TCompilationPerformance = Record<string, any>;
export type TOptimizationGains = Record<string, any>;
export type TLoadDistribution = Record<string, any>;
export type TNetworkUsage = Record<string, any>;
export type TStorageUsage = Record<string, any>;
export type TBaseMetrics = Record<string, any>;
export type TOptimizationMetrics = Record<string, any>;
export type TMLModel = Record<string, any>;
export type TConflictResolution = Record<string, any>;
export type TProcessingOptimization = Record<string, any>;
export type TProcessingContext = Record<string, any>;
export type TProcessingPipeline = Record<string, any>;
export type TPipelineResult = Record<string, any>;
export type TRollbackPoint = Record<string, any>;
export type TWorkflowExecution = Record<string, any>;
export type TOrchestrationState = Record<string, any>;
export type TSchedulePattern = Record<string, any>;
export type TTemplateOptimization = Record<string, any>;
export type TTemplatePerformance = Record<string, any>;
export type TTemplateMetadata = Record<string, any>;
export type TParameterValidation = Record<string, any>;
export type TParameterConstraints = Record<string, any>;