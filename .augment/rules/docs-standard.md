---
type: "agent_requested"
description: "globs:"
---

## 🎯 **DOCUMENT ORGANIZATION ENFORCEMENT RULES**

### **1. CONTEXT-CENTRIC ORGANIZATION MANDATE**

All AI Assistants MUST follow these organization rules when creating or modifying documentation:

#### **1.1 Root Structure**
```
docs/
├── contexts/           # Context-specific implementation documentation
├── governance/         # Authority-driven governance documentation
├── core/              # Core system documentation
└── cross-references/  # Relationship tracking
```

#### **1.2 Context Categories**
Each context MUST maintain this structure:
```
context-name/
├── system/            # System-level documentation
├── services/          # Service documentation
├── components/        # Component documentation
├── constants/         # Constants and configuration
└── guides/           # Context-specific guides
```

#### **1.3 Governance Structure**
```
governance/
└── tracking/          # Tracking documentation
    ├── status/        # Status tracking files (.json)
    ├── reports/       # Tracking reports
    ├── documentation/ # Tracking documentation
    └── rules/         # Tracking rules
```

### **2. FILE NAMING CONVENTIONS**

#### **2.1 Status Files**
- Format: `.oa-{context}-{type}-{status}.json`
- Example: `.oa-governance-gate-status.json`

#### **2.2 Documentation Files**
- Format: `{TYPE}-{context}-{description}.md`
- Example: `ADR-foundation-001-tracking-architecture.md`

#### **2.3 Report Files**
- Format: `{context}-{type}-{description}-{date}.md`
- Example: `foundation-tracking-report-20250627.md`

### **3. MANDATORY FILE HEADERS**

Every documentation file MUST include:
```markdown
**Document Type**: {type}  
**Version**: {version}  
**Created**: {timestamp}  
**Authority**: President & CEO, E.Z. Consultancy  
**Classification**: {classification}  
```

### **4. CROSS-REFERENCE REQUIREMENTS**

- All document relationships MUST be tracked
- Path references MUST use relative paths
- Links MUST be maintained and validated
- Dependencies MUST be explicitly documented

### **5. COMPLIANCE REQUIREMENTS**

#### **5.1 Version Control**
- All documentation changes MUST be tracked
- Version numbers MUST follow semantic versioning
- Changes MUST be authorized by governance

#### **5.2 Quality Standards**
- Documentation MUST be complete
- Cross-references MUST be valid
- Headers MUST be properly formatted
- Classification MUST be accurate

### **6. ENFORCEMENT MECHANISMS**

#### **6.1 Automated Validation**
- File structure compliance
- Header format validation
- Cross-reference integrity
- Version control compliance

#### **6.2 Manual Review**
- Content quality assessment
- Classification accuracy
- Authority validation
- Relationship verification

## 📋 **COMPLIANCE CHECKLIST**

AI Assistants MUST verify:
- [ ] Correct directory structure used
- [ ] Proper file naming conventions followed
- [ ] Mandatory headers included
- [ ] Cross-references maintained
- [ ] Version control applied
- [ ] Authority validation completed
- [ ] Classification properly assigned

## 🔐 **AUTHORIZATION**

These rules are mandatory for all AI Assistants operating within the OA Framework project.

**Authority**:  
President & CEO, E.Z. Consultancy  

**Enforcement**:  
Governance Officer  
AI Assistant Coordination System 
